<template>
  <view class="home-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: (statusBarHeight + safeAreaTop) + 'px' }">
      <view class="navbar-content">
        <view class="slogan">{{ homeData.slogan || '链接你我，共创未来' }}</view>
        <view class="add-btn" @click="publishTask">
          <u-icon name="plus" size="40" color="#ffffff"></u-icon>
        </view>
      </view>
    </view>

    <!-- 位置和搜索 -->
    <view class="location-search">
      <view class="location-search-row">
        <view class="location" @click="chooseLocation">
          <u-icon name="map" size="32" color="#666"></u-icon>
          <text class="location-text">{{ currentLocation || '获取位置中...' }}</text>
        </view>
        <view class="search-bar" @click="goSearch">
          <u-icon name="search" size="32" color="#999"></u-icon>
          <text class="search-placeholder">搜索任务、商品、商家</text>
          <view class="qr-btn" @click.stop="scanCode">
            <u-icon name="scan" size="32" color="#666"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 系统通知 -->
    <view class="notice-bar" v-if="notices.length > 0" @click="showNoticeList">
      <u-icon name="volume" size="32" color="#ff6b35"></u-icon>
      <swiper class="notice-swiper" vertical autoplay interval="3000" circular>
        <swiper-item v-for="notice in notices" :key="notice.noticeId">
          <text class="notice-text">{{ notice.noticeTitle }}</text>
        </swiper-item>
      </swiper>
      <u-icon name="arrow-right" size="24" color="#999"></u-icon>
    </view>

    <!-- 功能区域 -->
    <view class="function-area">
      <!-- 兴业助农 -->
      <view class="agriculture-card" @click="goAgriculture">
        <image class="agriculture-image" :src="agricultureConfig.imageUrl" mode="aspectFill"></image>
        <view class="agriculture-content">
          <text class="agriculture-title">{{ agricultureConfig.title }}</text>
          <text class="agriculture-desc">{{ agricultureConfig.content }}</text>
        </view>
      </view>

      <!-- 购物专区 -->
      <view class="shopping-card" @click="goShopping">
        <image class="shopping-image" :src="shoppingConfig.imageUrl" mode="aspectFill"></image>
        <text class="shopping-title">{{ shoppingConfig.title }}</text>
      </view>
    </view>

    <!-- 更多功能 -->
    <view class="more-functions">
      <view class="function-grid">
        <view class="function-item" v-for="func in functions" :key="func.id" @click="goFunction(func)">
          <image class="function-icon" :src="func.icon" mode="aspectFit"></image>
          <text class="function-name">{{ func.name }}</text>
        </view>
      </view>
    </view>

    <!-- 热门任务 -->
    <view class="hot-tasks">
      <view class="section-header">
        <text class="section-title">热门任务</text>
        <text class="more-btn" @click="goTaskList">更多</text>
      </view>
      <view class="task-list">
        <view class="task-item" v-for="task in hotTasks" :key="task.taskId" @click="goTaskDetail(task.taskId)">
          <view class="task-header">
            <view class="user-info">
              <image class="avatar" :src="task.publisherAvatar || '/static/default-avatar.png'" mode="aspectFill">
              </image>
              <view class="user-detail">
                <text class="username">{{ task.publisherName }}</text>
                <text class="publish-info">{{ formatTime(task.createTime) }} · {{ task.taskAddress }}</text>
              </view>
            </view>
            <view class="hot-score">
              <u-icon name="fire" size="24" color="#ff6b35"></u-icon>
              <text class="score">{{ task.hotScore || 0 }}</text>
            </view>
          </view>
          <view class="task-amount">¥{{ formatMoney(task.taskAmount) }}</view>
          <view class="task-title">{{ task.taskTitle }}</view>
          <view class="task-desc">{{ task.taskDesc }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getHomeData, getNotices, getAgricultureConfig, getShoppingConfig, getFunctions } from '@/api/home'
import { getHotTasks } from '@/api/task'
import { getCurrentLocation, formatTime, formatMoney, scanCode } from '@/utils/common'

export default {
  data() {
    return {
      statusBarHeight: 0,
      safeAreaTop: 0,
      homeData: {},
      currentLocation: '',
      notices: [],
      agricultureConfig: {
      },
      shoppingConfig: {
      },
      functions: [
        { id: 1, name: '我的钱包', icon: '/static/icons/wallet.png', url: '/pages/wallet/index' },
        { id: 2, name: '我的任务', icon: '/static/icons/task.png', url: '/pages/task/my' },
        { id: 3, name: '实名认证', icon: '/static/icons/auth.png', url: '/pages/user/auth' },
        { id: 4, name: '客服中心', icon: '/static/icons/service.png', url: '/pages/service/index' }
      ],
      hotTasks: [],
      longitude: '',
      latitude: ''
    }
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadHotTasks()
  },

  methods: {
    async initPage() {
      // 获取状态栏高度和安全区域
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight

      // 获取安全区域顶部高度，适配刘海屏
      if (systemInfo.safeAreaInsets) {
        this.safeAreaTop = systemInfo.safeAreaInsets.top || 0
      } else {
        // 兼容旧版本，对于iPhone X系列设备增加额外的安全距离
        const isIPhoneX = systemInfo.model && systemInfo.model.includes('iPhone') &&
          systemInfo.screenHeight >= 812 && systemInfo.statusBarHeight >= 44
        this.safeAreaTop = isIPhoneX ? 20 : 0
      }

      // 获取位置信息
      this.getLocation()

      // 加载页面数据
      this.loadHomeData()
      this.loadNotices()
      this.loadConfigs()
    },

    async getLocation() {
      try {
        const location = await getCurrentLocation()
        this.longitude = location.longitude
        this.latitude = location.latitude
        this.currentLocation = location.address || '当前位置'
      } catch (error) {
        console.error('获取位置失败:', error)
        this.currentLocation = '定位失败'
      }
    },

    async loadHomeData() {
      try {
        const res = await getHomeData({
          longitude: this.longitude,
          latitude: this.latitude
        })
        this.homeData = res.data || {}
      } catch (error) {
        console.error('加载首页数据失败:', error)
      }
    },

    async loadNotices() {
      try {
        const res = await getNotices()
        this.notices = res.data || []
      } catch (error) {
        console.error('加载通知失败:', error)
      }
    },

    async loadConfigs() {
      try {
        const [agriculture, shopping] = await Promise.all([
          getAgricultureConfig(),
          getShoppingConfig()
        ])

        if (agriculture.data) {
          this.agricultureConfig = agriculture.data
        }
        if (shopping.data) {
          this.shoppingConfig = shopping.data
        }
      } catch (error) {
        console.error('加载配置失败:', error)
      }
    },

    async loadHotTasks() {
      try {
        const res = await getHotTasks({
          longitude: this.longitude,
          latitude: this.latitude,
          limit: 10
        })
        this.hotTasks = res.data || []
      } catch (error) {
        console.error('加载热门任务失败:', error)
      }
    },

    publishTask() {
      uni.switchTab({
        url: '/pages/task/publish'
      })
    },

    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.currentLocation = res.name || res.address
          this.longitude = res.longitude
          this.latitude = res.latitude
          this.loadHotTasks()
        }
      })
    },

    goSearch() {
      uni.navigateTo({
        url: '/pages/search/index'
      })
    },

    async scanCode() {
      try {
        const result = await scanCode()
        // 处理扫码结果
        console.log('扫码结果:', result)
      } catch (error) {
        console.error('扫码失败:', error)
      }
    },

    showNoticeList() {
      uni.switchTab({
        url: '/pages/notice/list'
      })
    },

    goAgriculture() {
      if (this.agricultureConfig.linkUrl) {
        uni.navigateTo({
          url: this.agricultureConfig.linkUrl
        })
      }
    },

    goShopping() {
      if (this.shoppingConfig.linkUrl) {
        uni.navigateTo({
          url: this.shoppingConfig.linkUrl
        })
      }
    },

    goFunction(func) {
      if (func.url) {
        uni.navigateTo({
          url: func.url
        })
      }
    },

    goTaskList() {
      uni.switchTab({
        url: '/pages/task/list'
      })
    },

    goTaskDetail(taskId) {
      uni.navigateTo({
        url: `/pages/task/detail?id=${taskId}`
      })
    },

    formatTime,
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  background: #f8f8f8;
  min-height: 100vh;
}

.custom-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 40rpx;

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .slogan {
      color: #ffffff;
      font-size: 36rpx;
      font-weight: bold;
    }

    .add-btn {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.location-search {
  padding: 30rpx 40rpx;
  background: #ffffff;

  .location-search-row {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .location {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      min-width: 200rpx;

      .location-text {
        margin-left: 10rpx;
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 160rpx;
      }
    }

    .search-bar {
      display: flex;
      align-items: center;
      background: #f5f5f5;
      border-radius: 50rpx;
      padding: 20rpx 30rpx;
      flex: 1;

      .search-placeholder {
        flex: 1;
        margin-left: 20rpx;
        font-size: 28rpx;
        color: #999;
      }

      .qr-btn {
        margin-left: 20rpx;
      }
    }
  }
}

.notice-bar {
  display: flex;
  align-items: center;
  background: #fff7f0;
  padding: 20rpx 40rpx;
  margin-bottom: 20rpx;

  .notice-swiper {
    flex: 1;
    height: 60rpx;
    margin: 0 20rpx;

    .notice-text {
      font-size: 26rpx;
      color: #ff6b35;
      line-height: 60rpx;
    }
  }
}

.function-area {
  display: flex;
  padding: 0 40rpx;
  margin-bottom: 30rpx;
  gap: 20rpx;

  .agriculture-card {
    flex: 1;
    background: #ffffff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .agriculture-image {
      width: 100%;
      height: 200rpx;
    }

    .agriculture-content {
      padding: 20rpx;

      .agriculture-title {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      .agriculture-desc {
        font-size: 26rpx;
        color: #666;
      }
    }
  }

  .shopping-card {
    flex: 1;
    background: #ffffff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    position: relative;

    .shopping-image {
      width: 100%;
      height: 280rpx;
    }

    .shopping-title {
      position: absolute;
      bottom: 20rpx;
      left: 20rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
    }
  }
}

.more-functions {
  background: #ffffff;
  margin: 0 40rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .function-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 30rpx;

    .function-item {
      width: calc(25% - 22.5rpx);
      display: flex;
      flex-direction: column;
      align-items: center;

      .function-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 10rpx;
      }

      .function-name {
        font-size: 24rpx;
        color: #666;
        text-align: center;
      }
    }
  }
}

.hot-tasks {
  background: #ffffff;
  margin: 0 40rpx;
  border-radius: 20rpx;
  padding: 30rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .more-btn {
      font-size: 26rpx;
      color: #3cc51f;
    }
  }

  .task-list {
    .task-item {
      padding: 30rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .user-info {
          display: flex;
          align-items: center;

          .avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 40rpx;
            margin-right: 20rpx;
          }

          .user-detail {
            .username {
              display: block;
              font-size: 28rpx;
              color: #333;
              margin-bottom: 5rpx;
            }

            .publish-info {
              font-size: 24rpx;
              color: #999;
            }
          }
        }

        .hot-score {
          display: flex;
          align-items: center;

          .score {
            margin-left: 5rpx;
            font-size: 24rpx;
            color: #ff6b35;
          }
        }
      }

      .task-amount {
        font-size: 36rpx;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 10rpx;
      }

      .task-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      .task-desc {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }
}
</style>
